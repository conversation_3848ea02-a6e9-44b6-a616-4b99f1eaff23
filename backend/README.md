Todo API

# Todo Model
## Main Model
```go
type Todo struct {
	ID        uuid.UUID `json:"id"`
	Title     string    `json:"title"`
	Completed bool      `json:"completed"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
    // soft delete, nullable
    DeletedAt *time.Time `json:"deleted_at"`
}
```

## Input Model
```go
type TodoInput struct {
	Title string `json:"title" validate:"required"`
}
```

## Update Model
```go
type TodoUpdate struct {
	Title     *string `json:"title" validate:"required"`
	Completed *bool   `json:"completed"`
}
```

## List Model with pagination
```go
type TodoList struct {
	Todos []Todo `json:"todos"`
	Total int    `json:"total"`
    Page  int    `json:"page"`
    Limit int    `json:"limit"`
}
```

## Delete Batch Model
```go
type TodoDeleteBatch struct {
	Ids []uuid.UUID `json:"ids" validate:"required"`
}
```

# Endpoints

## Create Todo
```http
POST /todos
```

## Get Todo
```http
GET /todos/:id
```

## Update Todo
Partial update, at least one field is required.

```http
PATCH /todos/:id
```

## Delete Todo
```http
DELETE /todos/:id
```

## Batch Delete Todos
```http
POST /todos/batch
```

## List Todos
```http
GET /todos
```
