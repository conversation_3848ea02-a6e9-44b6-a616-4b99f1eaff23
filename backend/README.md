# TODO API

A modern, RESTful TODO API built with Go and Echo framework, following clean architecture principles and Go best practices.

## Features

- ✅ RESTful API endpoints for CRUD operations
- ✅ Clean architecture with separation of concerns
- ✅ In-memory storage (easily extensible to databases)
- ✅ Input validation and error handling
- ✅ Comprehensive unit and integration tests
- ✅ Configuration management via environment variables
- ✅ Graceful shutdown
- ✅ Rate limiting and security middleware
- ✅ JSON serialization/deserialization
- ✅ UUID-based todo IDs
- ✅ Proper HTTP status codes

## Project Structure

```
backend/
├── cmd/
│   └── main.go                 # Application entry point
├── internal/
│   ├── config/                 # Configuration management
│   │   └── config.go
│   ├── errors/                 # Custom error types
│   │   └── errors.go
│   ├── handlers/               # HTTP handlers
│   │   ├── todo.go
│   │   └── todo_test.go
│   ├── models/                 # Data models
│   │   ├── todo.go
│   │   └── todo_test.go
│   ├── repository/             # Data access layer
│   │   ├── todo.go
│   │   └── todo_test.go
│   ├── routes/                 # Route configuration
│   │   └── routes.go
│   └── service/                # Business logic layer
│       ├── todo.go
│       └── todo_test.go
├── go.mod
├── go.sum
└── README.md
```

## API Endpoints

### Base URL
```
http://localhost:8080/api/v1
```

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/todos` | Get all todos |
| GET | `/todos/{id}` | Get a specific todo by ID |
| POST | `/todos` | Create a new todo |
| PUT | `/todos/{id}` | Update an existing todo |
| DELETE | `/todos/{id}` | Delete a todo |
| GET | `/health` | Health check endpoint |

## Data Model

### Todo
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "Learn Go",
  "description": "Study Go programming language",
  "completed": false,
  "created_at": "2023-12-01T10:00:00Z",
  "updated_at": "2023-12-01T10:00:00Z"
}
```

### Create Todo Request
```json
{
  "title": "Learn Go",
  "description": "Study Go programming language"
}
```

### Update Todo Request
```json
{
  "title": "Learn Go Advanced",
  "description": "Study advanced Go concepts",
  "completed": true
}
```

## Getting Started

### Prerequisites
- Go 1.21 or higher
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies:
```bash
go mod tidy
```

3. Run the application:
```bash
go run cmd/main.go
```

The server will start on `http://localhost:8080`

### Configuration

The application can be configured using environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVER_HOST` | `0.0.0.0` | Server host address |
| `SERVER_PORT` | `8080` | Server port |

Example:
```bash
export SERVER_HOST=localhost
export SERVER_PORT=3000
go run cmd/main.go
```

## API Usage Examples

### Create a Todo
```bash
curl -X POST http://localhost:8080/api/v1/todos \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Learn Go",
    "description": "Study Go programming language"
  }'
```

### Get All Todos
```bash
curl http://localhost:8080/api/v1/todos
```

### Get a Specific Todo
```bash
curl http://localhost:8080/api/v1/todos/{todo-id}
```

### Update a Todo
```bash
curl -X PUT http://localhost:8080/api/v1/todos/{todo-id} \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Learn Go Advanced",
    "completed": true
  }'
```

### Delete a Todo
```bash
curl -X DELETE http://localhost:8080/api/v1/todos/{todo-id}
```

### Health Check
```bash
curl http://localhost:8080/api/v1/health
```

## Testing

Run all tests:
```bash
go test ./...
```

Run tests with coverage:
```bash
go test -cover ./...
```

Run tests with verbose output:
```bash
go test -v ./...
```

## Building

Build the application:
```bash
go build -o todo-api cmd/main.go
```

Run the built binary:
```bash
./todo-api
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `200 OK` - Successful GET, PUT requests
- `201 Created` - Successful POST requests
- `400 Bad Request` - Invalid request body or parameters
- `404 Not Found` - Todo not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server errors

Error response format:
```json
{
  "error": true,
  "message": "Error description"
}
```

## Validation Rules

### Todo Title
- Required
- Minimum length: 1 character
- Maximum length: 200 characters

### Todo Description
- Optional
- Maximum length: 1000 characters

## Architecture

This application follows clean architecture principles:

1. **Models Layer**: Defines data structures and business entities
2. **Repository Layer**: Handles data persistence (currently in-memory)
3. **Service Layer**: Contains business logic and validation
4. **Handler Layer**: Manages HTTP requests and responses
5. **Routes Layer**: Configures API endpoints and middleware

## Dependencies

- [Echo](https://echo.labstack.com/) - High performance, minimalist Go web framework
- [UUID](https://github.com/google/uuid) - UUID generation
- [Validator](https://github.com/go-playground/validator) - Struct validation
- [Testify](https://github.com/stretchr/testify) - Testing toolkit

## Future Enhancements

- [ ] Database integration (PostgreSQL, MySQL, MongoDB)
- [ ] Authentication and authorization
- [ ] Pagination for todo lists
- [ ] Filtering and sorting
- [ ] Todo categories/tags
- [ ] Due dates and reminders
- [ ] API documentation with Swagger
- [ ] Docker containerization
- [ ] Logging with structured logs
- [ ] Metrics and monitoring
