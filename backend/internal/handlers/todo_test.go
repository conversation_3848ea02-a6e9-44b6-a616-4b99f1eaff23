package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/migrations"
	"backend/internal/models"
	"backend/internal/repository"
	"backend/internal/service"
)

// setupTestRepository creates a PostgreSQL test repository
func setupTestRepository(t *testing.T) (repository.TodoRepository, func()) {
	// Skip if no test database is configured
	if os.Getenv("TEST_DB_HOST") == "" && os.Getenv("DATABASE_URL") == "" {
		t.Skip("Skipping PostgreSQL tests - no test database configured")
	}

	cfg := &config.DatabaseConfig{
		Host:     getEnvOrDefault("TEST_DB_HOST", "localhost"),
		Port:     5432,
		Name:     getEnvOrDefault("TEST_DB_NAME", "todo_test"),
		User:     getEnvOrDefault("TEST_DB_USER", "postgres"),
		Password: getEnvOrDefault("TEST_DB_PASSWORD", ""),
		SSLMode:  "prefer",
		MaxConns: 5,
		MinConns: 1,
	}

	// Create database connection
	db, err := database.NewPostgresDB(cfg)
	require.NoError(t, err, "Failed to connect to test database")

	// Run migrations
	migrator := migrations.NewMigrator(db.Pool)
	err = migrator.Up(context.Background())
	require.NoError(t, err, "Failed to run migrations")

	// Create repository
	repo := repository.NewPostgresTodoRepository(db.Pool)

	// Return cleanup function
	cleanup := func() {
		// Clean up test data
		_, _ = db.Pool.Exec(context.Background(), "DELETE FROM todos")
		db.Close()
	}

	return repo, cleanup
}

// getEnvOrDefault gets an environment variable or returns a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func setupTestHandler(t *testing.T) (*TodoHandler, *echo.Echo, func()) {
	repo, cleanup := setupTestRepository(t)
	svc := service.NewTodoService(repo)
	handler := NewTodoHandler(svc)
	e := echo.New()
	return handler, e, cleanup
}

func TestTodoHandler_CreateTodo(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	reqBody := models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusCreated, rec.Code)

	var response models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, reqBody.Title, response.Title)
	assert.Equal(t, reqBody.Description, response.Description)
	assert.NotEmpty(t, response.ID)
}

func TestTodoHandler_CreateTodoInvalidJSON(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusBadRequest, rec.Code)
}

func TestTodoHandler_GetTodo(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	// Create a todo first
	reqBody := models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	var created models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &created)
	require.NoError(t, err)

	// Now get the todo
	req = httptest.NewRequest(http.MethodGet, "/todos/"+created.ID, nil)
	rec = httptest.NewRecorder()
	c = e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(created.ID)

	err = handler.GetTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, created.ID, response.ID)
	assert.Equal(t, created.Title, response.Title)
}

func TestTodoHandler_GetTodoNotFound(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	req := httptest.NewRequest(http.MethodGet, "/todos/550e8400-e29b-41d4-a716-446655440000", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("550e8400-e29b-41d4-a716-446655440000")

	err := handler.GetTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusNotFound, rec.Code)
}

func TestTodoHandler_GetAllTodos(t *testing.T) {
	handler, e := setupTestHandler()

	req := httptest.NewRequest(http.MethodGet, "/todos", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.GetAllTodos(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Contains(t, response, "todos")
	assert.Contains(t, response, "count")
	assert.Equal(t, float64(0), response["count"]) // Initially empty
}

func TestTodoHandler_UpdateTodo(t *testing.T) {
	handler, e := setupTestHandler()

	// Create a todo first
	reqBody := models.CreateTodoRequest{
		Title:       "Original Title",
		Description: "Original Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	var created models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &created)
	require.NoError(t, err)

	// Update the todo
	newTitle := "Updated Title"
	completed := true
	updateReq := models.UpdateTodoRequest{
		Title:     &newTitle,
		Completed: &completed,
	}
	body, _ = json.Marshal(updateReq)

	req = httptest.NewRequest(http.MethodPut, "/todos/"+created.ID, bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec = httptest.NewRecorder()
	c = e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(created.ID)

	err = handler.UpdateTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, newTitle, response.Title)
	assert.True(t, response.Completed)
}

func TestTodoHandler_DeleteTodo(t *testing.T) {
	handler, e := setupTestHandler()

	// Create a todo first
	reqBody := models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	var created models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &created)
	require.NoError(t, err)

	// Delete the todo
	req = httptest.NewRequest(http.MethodDelete, "/todos/"+created.ID, nil)
	rec = httptest.NewRecorder()
	c = e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(created.ID)

	err = handler.DeleteTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response map[string]string
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Contains(t, response, "message")
}
