package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/handlers"
	"backend/internal/migrations"
	"backend/internal/repository"
	"backend/internal/routes"
	"backend/internal/service"
)

func main() {
	ctx := context.Background()

	// Load configuration
	cfg := config.Load()

	// Validate database configuration
	if err := cfg.Database.Validate(); err != nil {
		fmt.Printf("Database configuration error: %v\n", err)
		os.Exit(1)
	}

	// Initialize PostgreSQL database connection
	db, err := database.NewPostgresDB(&cfg.Database)
	if err != nil {
		fmt.Printf("Failed to connect to PostgreSQL database: %v\n", err)
		fmt.Printf("Please ensure PostgreSQL is running and database configuration is correct.\n")
		fmt.Printf("Required environment variables: DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD\n")
		os.Exit(1)
	}
	defer db.Close()

	// Run database migrations
	migrator := migrations.NewMigrator(db.Pool)
	if err := migrator.Up(ctx); err != nil {
		fmt.Printf("Failed to run database migrations: %v\n", err)
		fmt.Printf("Please ensure the database user has sufficient privileges to create tables and indexes.\n")
		os.Exit(1)
	}

	// Initialize dependencies
	todoRepo := repository.NewPostgresTodoRepository(db.Pool)
	todoService := service.NewTodoService(todoRepo)
	todoHandler := handlers.NewTodoHandler(todoService)

	// Initialize Echo
	e := echo.New()
	e.HideBanner = true

	// Global middleware
	e.Use(middleware.RateLimiter(middleware.NewRateLimiterMemoryStore(20)))
	e.Use(middleware.BodyLimit("2MB"))

	// Setup routes
	routes.SetupRoutes(e, todoHandler)

	// Start server in a goroutine
	go func() {
		if err := e.Start(fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)); err != nil {
			e.Logger.Error("shutting down the server", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit

	// Graceful shutdown
	shutdownCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	if err := e.Shutdown(shutdownCtx); err != nil {
		e.Logger.Error(err)
	}
}
