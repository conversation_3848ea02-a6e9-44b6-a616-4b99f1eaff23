package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func main() {

	ctx := context.Background()

	// use config
	serverAddr := "0.0.0.0"
	serverPort := 8080

	e := echo.New()
	e.HideBanner = true
	e.Use(
		// middleware.Logger(),
		middleware.RateLimiter(middleware.NewRateLimiterMemoryStore(20)),
		middleware.BodyLimit("2KB"),
		middleware.CSRF(),
	)

	go func() {
		if err := e.Start(fmt.Sprintf("%s:%d", serverAddr, serverPort)); err != nil {
			e.Logger.Error("shutting down the server", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit

	shutdownCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	if err := e.Shutdown(shutdownCtx); err != nil {
		e.Logger.Error(err)
	}
}
